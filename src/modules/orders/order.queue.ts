import { OnQueueActive, OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { JOBS, QUEUES } from '../../enums/queues.enum';
import { getDocId, millify } from '../../utils/functions';
import { User } from '../user/user.schema';
import { OrdersService } from './orders.service';
import { CURRENCIES } from '../country/country.schema';
import { ORDER_STATUSES } from './order.schema';
import { StoreDocument } from '../store/store.schema';
import { Types } from 'mongoose';
import { JobData } from 'aws-sdk/clients/codepipeline';
import { INVOICE_STATUSES, Invoice } from '../invoice/invoice.schema';
import { StockThresholdService } from '../stock-threshold/stock-threshold.service';

export interface OrderJob<T> {
  type: JOBS;
  data: T;
}

export type OrderVolumeMilestone = {
  store: string;
  currency: CURRENCIES;
  user: string;
  order_id: string;
};

export type OrderCountMilestone = { store: string; user: string; order_id: string };
export type OrderPaymentData = { order_id: string; url: string };

@Processor(QUEUES.ORDER)
export class OrderQueue {
  constructor(private orderService: OrdersService) {}

  @OnQueueActive()
  onActive(job: Job) {
    console.log(`Processing job ${job.id} of type ${job.name} with data ${job.data}...`);
  }

  @OnQueueFailed()
  onFailed(job: Job, error: Error) {
    console.log(`Failed to process job ${job.id} of type ${job.name} with data, error: ${error.message}`);
  }
  @OnQueueCompleted()
  onCompleted(job: Job, result: any) {
    console.log(`Job ${job.id} of type ${job.name} successfully completed, result: ${result}`);
    job.remove().catch(console.log);
  }

  @Process(QUEUES.ORDER)
  async handleOrderJobs(job: Job<OrderJob<any>>) {
    switch (job.data.type) {
      case JOBS.ORDER_COUNT_MILESTONE:
        await this.checkOrderCountMilestone(job);
        break;
      case JOBS.ORDER_VOLUME_MILESTONE:
        await this.checkOrderVolumeMilestone(job);
        break;
      case JOBS.CHECK_ORDER_PAYMENT:
        await this.checkOrderPaymentStatus(job);
        break;
      default:
      // DO NOTHING
    }
  }

  async checkOrderVolumeMilestone(job: Job<OrderJob<OrderVolumeMilestone>>) {
    const { store, currency, user, order_id } = job.data.data;

    const _milestones = {
      [CURRENCIES.NGN]: [100_000, 500_000, 1_000_000, 5_000_000, 10_000_000, 50_000_000, 100_000_000],
      [CURRENCIES.GHC]: [5_000, 10_000, 50_000, 100_000, 500_000, 1_000_000, 5_000_000, 10_000_000],
      [CURRENCIES.ZAR]: [5_000, 10_000, 50_000, 100_000, 500_000, 1_000_000, 5_000_000],
      [CURRENCIES.KES]: [10_000, 50_000, 100_000, 500_000, 1_000_000, 5_000_000, 10_000_000],
      [CURRENCIES.USD]: [1_000, 5_000, 10_000, 50_000, 100_000, 500_000, 1_000_000],
      [CURRENCIES.GBP]: [1_000, 5_000, 10_000, 50_000, 100_000, 500_000, 1_000_000],
      [CURRENCIES.EUR]: [1_000, 5_000, 10_000, 50_000, 100_000, 500_000, 1_000_000],
    };

    const milestones = _milestones[currency];

    const order = await this.orderService.orderModel.findById(order_id);

    if ([ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED].includes(order.status)) {
      job.remove().catch(console.log);
      return;
    }

    const currentTotal = await this.orderService.getTotalStoreOrderAmount({
      currency,
      store: Types.ObjectId(store) as any,
    });
    const initialTotal = currentTotal - order.total_amount;

    if (initialTotal < milestones[0] && currentTotal < milestones[0]) {
      job.remove().catch(console.log);
      return;
    }

    const dbUser = await this.orderService.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, {
        _id: user,
      })
      .toPromise();

    for (let m of milestones) {
      if (initialTotal < m && currentTotal >= m) {
        const millifiedValue = millify(m);

        await this.trackMilestone(store, { 'milestones.order_volume': { milestone: m, date: new Date() } });

        await this.orderService.resend.sendEmail(BROKER_PATTERNS.MAIL.SALES_MILESTONE, {
          to: dbUser.email,
          subject: `You’ve crossed ${millifiedValue} in sales 🤯`,
          data: {
            name: dbUser.name.split(' ')[0],
            value: millifiedValue,
            currency,
          },
        });
        break;
      }
    }

    job.remove().catch(console.log);
    return { completed: true };
  }

  async checkOrderCountMilestone(job: Job<OrderJob<OrderCountMilestone>>) {
    const { store, user, order_id } = job.data.data;

    const milestones = [10, 50, 100, 500, 1_000, 5_000, 10_000, 50_000];

    const order = await this.orderService.orderModel.findById(order_id).populate('customer');

    if ([ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED].includes(order.status)) {
      job.remove().catch(console.log);
      return;
    }

    const dbUser = await this.orderService.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, {
        _id: user,
      })
      .toPromise();

    this.orderService.customerIoRepository.trackUserEvent({
      userId: getDocId(dbUser),
      name: 'order',
      data: {
        currency: order?.currency,
        amount: order?.total_amount,
        items: order?.items.map((i) => i.snapshot?.name).join(', '),
        customer: order?.customer?.name,
      },
    });

    const currentTotal = await this.orderService.orderModel.countDocuments({
      store,
      status: { $nin: [ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED] },
    });

    if (currentTotal - 1 < milestones[0] && currentTotal < milestones[0]) {
      job.remove().catch(console.log);
      return;
    }

    for (let m of milestones) {
      if (currentTotal - 1 < m && currentTotal >= m) {
        const millifiedValue = millify(m);

        await this.trackMilestone(store, { 'milestones.total_orders': { milestone: m, date: new Date() } });

        this.orderService.resend.sendEmail(BROKER_PATTERNS.MAIL.ORDERS_MILESTONE, {
          to: dbUser.email,
          subject: `🎊 Congrats on reaching ${millifiedValue} orders!`,
          data: {
            name: dbUser.name.split(' ')[0],
            value: millifiedValue,
          },
        });
        break;
      }
    }

    job.remove().catch(console.log);
    return { completed: true };
  }

  async checkOrderPaymentStatus(job: Job<OrderJob<OrderPaymentData>>) {
    const { order_id, url } = job.data.data;
    const order = await this.orderService.orderModel.findById(order_id);

    if ([ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED].includes(order.status)) {
      job.remove().catch(console.log);
      return;
    }

    if (!order.is_paid) {
      await this.orderService.updateStatus(
        order.store as string,
        order.id,
        { status: ORDER_STATUSES.ABANDONED, reason: 'You took too long to pay for this order' },
        `${process.env.CATLOG_WWW}/orders/${order.id}`,
      );
    }

    job.remove().catch(console.log);
    return { completed: true };

    // const invoice = await this.orderService.brokerTransport
    //   .send<Invoice>(BROKER_PATTERNS.INVOICE.GET_INVOICE, {
    //     _id: order?.invoice,
    //   })
    //   .toPromise();

    // if (invoice.status !== INVOICE_STATUSES.PAID && invoice.status !== INVOICE_STATUSES.PAYMENT_IN_PROGRESS) {
    //   await this.orderService.updateStatus(order.store as string, order.id, { status: ORDER_STATUSES.CANCELLED }, url);
    //   await this.orderService.brokerTransport
    //     .send<Invoice>(BROKER_PATTERNS.INVOICE.UPDATE_TO_EXPIRED, {
    //       invoice_id: invoice.id,
    //       store_id: order.store,
    //     })
    //     .toPromise();
    // }
  }

  async trackMilestone(store: string, data: any) {
    return await this.orderService.brokerTransport
      .send<StoreDocument>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
        filter: {
          _id: store,
        },
        update: {
          $push: data,
        },
      })
      .toPromise();
  }
}
