import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { StockThresholdService } from './stock-threshold.service';
import { Store, StoreSchema } from '../store/store.schema';
import { Item, ItemSchema } from '../item/item.schema';
import { Order, OrderSchema } from '../orders/order.schema';
import { BrokerTransportService } from '../../broker/broker-transport.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Store.name, schema: StoreSchema },
      { name: Item.name, schema: ItemSchema },
      { name: Order.name, schema: OrderSchema },
    ]),
  ],
  providers: [StockThresholdService, BrokerTransportService],
  exports: [StockThresholdService],
})
export class StockThresholdModule {}
