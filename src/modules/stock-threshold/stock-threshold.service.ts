import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Store, StoreDocument } from '../store/store.schema';
import { Item, ItemDocument } from '../item/item.schema';
import { Order, OrderDocument } from '../orders/order.schema';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';

export interface StockThresholdData {
  store: Store;
  item: Item;
  variantId?: string;
  variantName?: string;
  currentQuantity: number;
  threshold: number;
  orderId: string;
}

@Injectable()
export class StockThresholdService {
  constructor(
    @InjectModel(Store.name) private storeModel: Model<StoreDocument>,
    @InjectModel(Item.name) private itemModel: Model<ItemDocument>,
    @InjectModel(Order.name) private orderModel: Model<OrderDocument>,
    private readonly brokerTransportService: BrokerTransportService,
  ) {}

  async checkStockThreshold(orderId: string): Promise<void> {
    try {
      const order = await this.orderModel
        .findById(orderId)
        .populate('store')
        .exec();

      if (!order || !order.store) {
        return;
      }

      const store = order.store as Store;
      const globalThreshold = store.global_stock_threshold || 5;

      for (const orderItem of order.items) {
        const item = await this.itemModel.findById(orderItem.item_id).exec();
        
        if (!item) continue;

        const itemThreshold = item.stock_threshold || globalThreshold;

        if (orderItem.variant_id) {
          const variant = item.variants?.options?.find(option => 
            option._id?.toString() === orderItem.variant_id?.toString()
          );
          
          if (variant) {
            const variantQuantity = variant.quantity || 0;
            
            if (variantQuantity <= itemThreshold) {
              const thresholdData: StockThresholdData = {
                store,
                item,
                variantId: variant._id?.toString(),
                variantName: JSON.stringify(variant.values),
                currentQuantity: variantQuantity,
                threshold: itemThreshold,
                orderId,
              };

              await this.sendStockThresholdNotifications(thresholdData);
            }
          }
        } else {
          const itemQuantity = item.quantity || 0;
          
          if (itemQuantity <= itemThreshold) {
            const thresholdData: StockThresholdData = {
              store,
              item,
              currentQuantity: itemQuantity,
              threshold: itemThreshold,
              orderId,
            };

            await this.sendStockThresholdNotifications(thresholdData);
          }
        }
      }
    } catch (error) {
      console.error('Error checking stock threshold:', error);
    }
  }

  private async sendStockThresholdNotifications(data: StockThresholdData): Promise<void> {
    try {
      this.brokerTransportService.emit(
        BROKER_PATTERNS.MAIL.STOCK_THRESHOLD_NOTIFICATION,
        data,
      );

      this.brokerTransportService.emit(
        BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION,
        {
          userId: data.store.owner,
          title: 'Low Stock Alert',
          body: data.variantId 
            ? `${data.item.name} (${data.variantName}) is running low on stock (${data.currentQuantity} remaining)`
            : `${data.item.name} is running low on stock (${data.currentQuantity} remaining)`,
          data: {
            type: 'stock_threshold',
            itemId: data.item._id,
            storeId: data.store._id,
            variantId: data.variantId,
            orderId: data.orderId,
          },
        },
      );
    } catch (error) {
      console.error('Error sending stock threshold notifications:', error);
    }
  }
}
